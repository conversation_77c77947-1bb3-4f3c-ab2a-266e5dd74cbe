"use client";

import * as React from "react";
import { format, parse } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";

interface DatePickerProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  disabled?: boolean;
}

export function DatePicker({
  value,
  onChange,
  className,
  disabled,
}: DatePickerProps) {
  const [inputValue, setInputValue] = React.useState<string>("");
  const [isMounted, setIsMounted] = React.useState(false);
  const dateInputRef = React.useRef<HTMLInputElement>(null);

  // Handle hydration
  React.useEffect(() => {
    setIsMounted(true);
    if (value) {
      try {
        const date = parse(value, "yyyy-MM-dd", new Date());
        if (!isNaN(date.getTime())) {
          setInputValue(format(date, "dd/MM/yyyy"));
        }
      } catch (error) {
        setInputValue("");
      }
    } else {
      setInputValue("");
    }
  }, [value]);

  const handleTextInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const str = e.target.value;

    // Auto-format as user types DD/MM/YYYY
    let formatted = str.replace(/[^\d]/g, "");
    if (formatted.length >= 2) {
      formatted = formatted.substring(0, 2) + "/" + formatted.substring(2);
    }
    if (formatted.length >= 5) {
      formatted = formatted.substring(0, 5) + "/" + formatted.substring(5, 9);
    }

    // Limit to 10 characters (DD/MM/YYYY)
    if (formatted.length > 10) {
      formatted = formatted.substring(0, 10);
    }

    setInputValue(formatted);

    // Try to parse complete date
    if (formatted.length === 10) {
      try {
        const date = parse(formatted, "dd/MM/yyyy", new Date());
        if (!isNaN(date.getTime())) {
          onChange(format(date, "yyyy-MM-dd"));
        }
      } catch (error) {
        // Invalid date, ignore
      }
    }
  };

  const handleCalendarClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (dateInputRef.current && !disabled) {
      // Remove pointer-events-none temporarily to allow interaction
      dateInputRef.current.style.pointerEvents = "auto";

      // Focus and trigger the picker
      dateInputRef.current.focus();

      // Small delay to ensure focus is set
      setTimeout(() => {
        if (dateInputRef.current) {
          if (dateInputRef.current.showPicker) {
            try {
              dateInputRef.current.showPicker();
            } catch (error) {
              dateInputRef.current.click();
            }
          } else {
            dateInputRef.current.click();
          }

          // Restore pointer-events-none after interaction
          setTimeout(() => {
            if (dateInputRef.current) {
              dateInputRef.current.style.pointerEvents = "none";
            }
          }, 100);
        }
      }, 10);
    }
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const dateValue = e.target.value;
    onChange(dateValue);
  };

  // Prevent hydration issues by not rendering complex parts until mounted
  if (!isMounted) {
    return (
      <div className={cn("relative", className)}>
        <Input
          type="text"
          placeholder="DD/MM/YYYY"
          value=""
          disabled={disabled}
          className="pr-10"
          readOnly
        />
        <div className="absolute right-3 top-1/2 -translate-y-1/2">
          <CalendarIcon className="h-4 w-4 text-gray-500" />
        </div>
      </div>
    );
  }

  return (
    <div className={cn("relative", className)}>
      {/* Hidden native date input */}
      <input
        ref={dateInputRef}
        type="date"
        value={value}
        onChange={handleDateChange}
        disabled={disabled}
        className="absolute inset-0 w-full h-full opacity-0 pointer-events-none z-0"
        tabIndex={-1}
        style={{
          colorScheme: "light",
        }}
      />

      {/* Visual input field */}
      <Input
        type="text"
        placeholder="DD/MM/YYYY"
        value={inputValue}
        onChange={handleTextInputChange}
        disabled={disabled}
        className="pr-10"
      />

      {/* Calendar icon - clickable */}
      <button
        type="button"
        onClick={handleCalendarClick}
        disabled={disabled}
        className="absolute right-3 top-1/2 -translate-y-1/2 p-1 hover:bg-gray-100 rounded transition-colors z-10"
        aria-label="Open calendar"
      >
        <CalendarIcon className="h-4 w-4 text-gray-500" />
      </button>
    </div>
  );
}
