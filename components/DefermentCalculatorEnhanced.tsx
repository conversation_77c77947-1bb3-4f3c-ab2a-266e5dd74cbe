"use client";

import React, { useState, useEffect } from "react";
import {
  Calendar,
  BookO<PERSON>,
  Calculator,
  Plus,
  Trash2,
  AlertCircle,
  CheckCircle2,
  Save,
  RotateCcw,
  Copy,
  Info,
  History,
  Eye,
  ExternalLink,
  DollarSign,

} from "lucide-react";
import {
  adjustToMonday,
  adjustToEndOfWeekSunday,
  daysBetween,
  round,
} from "@/lib/utils/dateUtils";
import { format, addDays } from "date-fns";
import { DatePicker } from "@/components/ui/DatePicker";
import { CourseSearchInput } from "@/components/ui/CourseSearchInput";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import {
  FeeAllocationInput,
  FeeAllocationResult,
  calculateFeeAllocation,
  validateFeeAllocationInput,
} from "@/lib/utils/feeAllocationUtils";
import {
  saveDefermentCalculation
} from "@/lib/supabase/defermentService";

export interface Course {
  id: number;
  courseName: string;
  // First course fields
  defermentStartDate: string;
  resumptionDate: string;
  // All courses
  originalStartDate: string;
  originalEndDate: string;
  // Subsequent courses
  newStartDate: string;

  // Duration input fields (user can enter duration instead of end date)
  durationWeeks?: number;
  durationDays?: number;
  durationMonths?: number;
  durationType?: 'weeks' | 'days' | 'months';

  // Calculated values
  duration: number; // in weeks
  defermentDuration: number;
  newCalculatedStartDate: string;
  newEndDate: string;

  // Payment plan fee calculation fields (optional)
  totalMaterialFee?: number;
  totalPlacementFee?: number;
  totalTuitionFee?: number;
  prepaymentTotal?: number;

  // Payment calculation results
  totalAllFees?: number;
  prepaidAmount?: number;
  prepaidTuitionFee?: number;
  prepaidMaterialFee?: number;
  prepaidPlacementFee?: number;
  otherPrePaidNonTuitionFee?: number;
  coverageStartDate?: string;
  coverageEndDate?: string;

  // Fee allocation fields
  studentId?: string;
  studentName?: string;
  courseCode?: string;
  tuitionFeesPaidSoFar?: number;
  materialsFeesPaid?: number;
  courseCommencementDate?: string;

  // Fee allocation results
  feeAllocationResult?: FeeAllocationResult;
}

type CourseErrors = {
  [key: string]: string | undefined;
};

type AllErrors = {
  [courseId: number]: CourseErrors;
};

interface CalculationHistory {
  id: string;
  timestamp: string;
  courses: Course[];
  title: string;
}

const STORAGE_KEY = "deferment-calculator-data";
const HISTORY_KEY = "deferment-calculator-history";

export default function DefermentCalculatorEnhanced() {
  const [courses, setCourses] = useState<Course[]>([
    {
      id: 1,
      courseName: "",
      originalStartDate: "",
      originalEndDate: "",
      defermentStartDate: "",
      resumptionDate: "",
      newStartDate: "",
      durationWeeks: 0,
      durationDays: 0,
      durationMonths: 0,
      durationType: 'weeks',
      duration: 0,
      defermentDuration: 0,
      newCalculatedStartDate: "",
      newEndDate: "",
    },
  ]);
  const [errors, setErrors] = useState<AllErrors>({});
  const [isCalculating, setIsCalculating] = useState(false);
  const [lastSaved, setLastSaved] = useState<string>("");
  const [calculationHistory, setCalculationHistory] = useState<
    CalculationHistory[]
  >([]);
  const [showHistory, setShowHistory] = useState(false);
  const [includePaymentCalculation, setIncludePaymentCalculation] = useState(false);

  // Database-related state
  const [isSavingToDatabase, setIsSavingToDatabase] = useState(false);

  // Load data from localStorage on component mount
  useEffect(() => {
    try {
      const savedData = localStorage.getItem(STORAGE_KEY);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        setCourses(parsedData.courses || [
          { courseName: "", startDate: "", endDate: "", intakeDate: "" },
          { courseName: "", startDate: "", endDate: "", intakeDate: "" },
          { courseName: "", startDate: "", endDate: "", intakeDate: "" },
          { courseName: "", startDate: "", endDate: "", intakeDate: "" },
        ]);
        setLastSaved(parsedData.timestamp || "");
      }

      // Load calculation history
      const historyData = localStorage.getItem(HISTORY_KEY);
      if (historyData) {
        const parsedHistory = JSON.parse(historyData);
        setCalculationHistory(parsedHistory);
      }
    } catch (error) {
      console.error("Error loading saved data:", error);
    }
  }, []);

  // Auto-save to localStorage whenever courses change
  useEffect(() => {
    if (courses.length > 0 && courses[0].courseName) {
      try {
        const dataToSave = {
          courses,
          timestamp: new Date().toLocaleString(),
        };
        localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
        setLastSaved(dataToSave.timestamp);
      } catch (error) {
        console.error("Error saving data:", error);
      }
    }
  }, [courses]);

  // Clear all data and localStorage
  const clearAllData = () => {
    if (
      confirm("Are you sure you want to clear all data? This cannot be undone.")
    ) {
      localStorage.removeItem(STORAGE_KEY);
      setCourses([
        {
          id: 1,
          courseName: "",
          originalStartDate: "",
          originalEndDate: "",
          defermentStartDate: "",
          resumptionDate: "",
          newStartDate: "",
          durationWeeks: 0,
          durationDays: 0,
          durationMonths: 0,
          durationType: 'weeks',
          duration: 0,
          defermentDuration: 0,
          newCalculatedStartDate: "",
          newEndDate: "",
        },
      ]);
      setErrors({});
      setLastSaved("");
    }
  };

  // Load calculation from history
  const loadFromHistory = (historyItem: CalculationHistory) => {
    setCourses(historyItem.courses);
    setShowHistory(false);
  };

  // Copy text to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // You could add a toast notification here
    } catch (error) {
      console.error("Failed to copy:", error);
    }
  };

  // Clear history
  const clearHistory = () => {
    if (confirm("Are you sure you want to clear calculation history?")) {
      setCalculationHistory([]);
      localStorage.removeItem(HISTORY_KEY);
    }
  };

  // Save calculation to database
  const saveToDatabase = async () => {
    const title = prompt("Enter a name for this calculation:") ||
      `Calculation ${new Date().toLocaleDateString()}`;

    if (!title.trim()) return;

    setIsSavingToDatabase(true);
    try {
      const completedCourses = courses.filter((course) => course.newEndDate);
      if (completedCourses.length === 0) {
        alert("Please complete the calculation before saving to database.");
        return;
      }

      const result = await saveDefermentCalculation({
        title: title.trim(),
        courses: completedCourses,
        includePaymentCalculation,
        notes: `Saved on ${new Date().toLocaleString()}`
      });

      if (result.success) {
        alert("Calculation saved successfully!");
      } else {
        alert(`Failed to save calculation: ${result.error}`);
      }
    } catch (error) {
      console.error("Error saving to database:", error);
      alert("An unexpected error occurred while saving.");
    } finally {
      setIsSavingToDatabase(false);
    }
  };

  // Load saved calculations from database


  const handleCourseChange = (id: number, field: keyof Course, value: any) => {
    setCourses((prevCourses) =>
      prevCourses.map((course) =>
        course.id === id ? { ...course, [field]: value } : course
      )
    );
  };

  // Handle duration changes and auto-calculate end date
  const handleDurationChange = (courseId: number, duration: number, type: 'weeks' | 'days' | 'months') => {
    const course = courses.find(c => c.id === courseId);
    if (!course || !course.originalStartDate || !duration || duration <= 0) return;

    const startDate = new Date(course.originalStartDate);
    const calculatedEndDate = calculateEndDateFromDuration(startDate, duration, type);

    // Calculate all duration types for consistency
    const durations = calculateDurationFromDates(startDate, calculatedEndDate);

    setCourses(prevCourses =>
      prevCourses.map(c =>
        c.id === courseId
          ? {
              ...c,
              durationType: type,
              durationWeeks: durations.weeks,
              durationDays: durations.days,
              durationMonths: durations.months,
              originalEndDate: format(calculatedEndDate, "yyyy-MM-dd"),
            }
          : c
      )
    );
  };

  // Handle end date changes and auto-calculate duration
  const handleEndDateChange = (courseId: number, endDate: string) => {
    const course = courses.find(c => c.id === courseId);
    if (!course || !course.originalStartDate || !endDate) return;

    const startDate = new Date(course.originalStartDate);
    const endDateObj = new Date(endDate);

    // Ensure end date is on Sunday
    const adjustedEndDate = adjustToEndOfWeekSunday(endDateObj);
    const durations = calculateDurationFromDates(startDate, adjustedEndDate);

    setCourses(prevCourses =>
      prevCourses.map(c =>
        c.id === courseId
          ? {
              ...c,
              originalEndDate: format(adjustedEndDate, "yyyy-MM-dd"),
              durationWeeks: durations.weeks,
              durationDays: durations.days,
              durationMonths: durations.months,
              durationType: course.durationType || 'weeks', // Keep current type
            }
          : c
      )
    );
  };

  const addCourse = () => {
    if (courses.length < 4) {
      setCourses((prev) => [
        ...prev,
        {
          id: prev.length + 1,
          courseName: "",
          originalStartDate: "",
          originalEndDate: "",
          defermentStartDate: "",
          resumptionDate: "",
          newStartDate: "",
          durationWeeks: 0,
          durationDays: 0,
          durationMonths: 0,
          durationType: 'weeks',
          duration: 0,
          defermentDuration: 0,
          newCalculatedStartDate: "",
          newEndDate: "",
        },
      ]);
    }
  };

  const removeCourse = (id: number) => {
    if (courses.length > 1) {
      setCourses((prev) => prev.filter((course) => course.id !== id));
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[id];
        return newErrors;
      });
    }
  };

  const validate = (): boolean => {
    const newErrors: AllErrors = {};
    let isValid = true;

    courses.forEach((course) => {
      const courseErrors: CourseErrors = {};
      if (!course.courseName) {
        courseErrors.courseName = "Course name is required.";
        isValid = false;
      }
      if (!course.originalStartDate) {
        courseErrors.originalStartDate = "Original start date is required.";
        isValid = false;
      }
      if (!course.originalEndDate) {
        courseErrors.originalEndDate = "Original end date is required.";
        isValid = false;
      }

      if (course.id === 1) {
        if (!course.defermentStartDate) {
          courseErrors.defermentStartDate = "Deferment start date is required.";
          isValid = false;
        }
        if (!course.resumptionDate) {
          courseErrors.resumptionDate = "Resumption date is required.";
          isValid = false;
        }
      } else {
        if (!course.newStartDate) {
          courseErrors.newStartDate = "New start date is required.";
          isValid = false;
        }
      }

      // Payment validation if enabled
      if (includePaymentCalculation) {
        if (!course.totalTuitionFee || course.totalTuitionFee <= 0) {
          courseErrors.totalTuitionFee = "Tuition fee is required when payment calculation is enabled.";
          isValid = false;
        }
        if (course.totalMaterialFee && course.totalMaterialFee < 0) {
          courseErrors.totalMaterialFee = "Material fee cannot be negative.";
          isValid = false;
        }
        if (course.totalPlacementFee && course.totalPlacementFee < 0) {
          courseErrors.totalPlacementFee = "Placement fee cannot be negative.";
          isValid = false;
        }
        if (course.prepaymentTotal && course.prepaymentTotal < 0) {
          courseErrors.prepaymentTotal = "Prepayment total cannot be negative.";
          isValid = false;
        }
      }

      if (Object.keys(courseErrors).length > 0) {
        newErrors[course.id] = courseErrors;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleCalculate = () => {
    if (!validate()) {
      return;
    }

    setIsCalculating(true);
    const calculatedCourses = courses.map((course, index) => {
      const originalStartDate = new Date(course.originalStartDate);
      const originalEndDate = new Date(course.originalEndDate);

      const duration = round(
        daysBetween(originalStartDate, originalEndDate) / 7
      );

      let newCalculatedStartDate: Date;
      let newEndDate: Date;
      let defermentDuration = 0;

      if (index === 0) {
        const defermentStartDate = new Date(course.defermentStartDate);
        const resumptionDate = new Date(course.resumptionDate);

        defermentDuration = round(
          daysBetween(defermentStartDate, resumptionDate) / 7
        );

        newCalculatedStartDate = adjustToMonday(resumptionDate, "next");
        // Correct logic: Add deferment duration to original end date
        newEndDate = adjustToEndOfWeekSunday(
          addWeeks(originalEndDate, defermentDuration)
        );
      } else {
        newCalculatedStartDate = adjustToMonday(
          new Date(course.newStartDate),
          "preceding"
        );
        newEndDate = adjustToEndOfWeekSunday(
          addWeeks(newCalculatedStartDate, duration)
        );
      }

      // Calculate fee allocation if payment calculation is enabled and it's the first course
      let feeAllocationResult: FeeAllocationResult | undefined;
      let newTotalTuitionFee = course.totalTuitionFee || 0;

      if (includePaymentCalculation && index === 0 && course.totalTuitionFee) {
        // Calculate new total fee using the formula: A - B + C
        // A = Total tuition fees payable as per FLOO
        // B = Tuition fees paid until now
        // C = Unspent tuition fees
        newTotalTuitionFee = (course.totalTuitionFee || 0) - (course.tuitionFeesPaidSoFar || 0) + (course.prepaymentTotal || 0);

        const feeAllocationInput: FeeAllocationInput = {
          studentId: course.studentId || 'N/A',
          studentName: course.studentName || 'N/A',
          courseName: course.courseName,
          courseCode: course.courseCode || 'N/A',
          totalTuitionFee: newTotalTuitionFee,
          tuitionFeesPaidSoFar: course.tuitionFeesPaidSoFar || 0,
          materialsFeesPaid: course.totalMaterialFee || 0,
          originalCourseStartDate: course.originalStartDate,
          defermentStartDate: course.defermentStartDate,
          courseCommencementDate: course.courseCommencementDate,
        };

        const validation = validateFeeAllocationInput(feeAllocationInput);
        if (validation.isValid) {
          feeAllocationResult = calculateFeeAllocation(feeAllocationInput);
        }
      }

      // Calculate payment fees with the new total tuition fee
      const paymentResults = calculatePaymentFees(course, newCalculatedStartDate, newEndDate, newTotalTuitionFee);

      return {
        ...course,
        duration,
        defermentDuration,
        newCalculatedStartDate: format(newCalculatedStartDate, "dd/MM/yyyy (EEEE)"),
        newEndDate: format(newEndDate, "dd/MM/yyyy (EEEE)"),
        ...paymentResults,
        feeAllocationResult,
      };
    });

    setCourses(calculatedCourses);
    setIsCalculating(false);

    // Auto-save to history after successful calculation
    setTimeout(() => {
      const historyItem: CalculationHistory = {
        id: Date.now().toString(),
        timestamp: new Date().toLocaleString(),
        courses: calculatedCourses,
        title: `Auto-saved ${new Date().toLocaleDateString()}`,
      };

      const updatedHistory = [historyItem, ...calculationHistory].slice(0, 10);
      setCalculationHistory(updatedHistory);
      localStorage.setItem(HISTORY_KEY, JSON.stringify(updatedHistory));
    }, 100);
  };

  const addWeeks = (date: Date, weeks: number) => {
    const newDate = new Date(date);
    newDate.setDate(newDate.getDate() + weeks * 7);
    return newDate;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Duration calculation utilities
  const calculateEndDateFromDuration = (startDate: Date, duration: number, type: 'weeks' | 'days' | 'months'): Date => {
    const endDate = new Date(startDate);

    switch (type) {
      case 'weeks':
        endDate.setDate(endDate.getDate() + (duration * 7) - 1); // -1 to make it inclusive
        break;
      case 'days':
        endDate.setDate(endDate.getDate() + duration - 1); // -1 to make it inclusive
        break;
      case 'months':
        endDate.setMonth(endDate.getMonth() + duration);
        endDate.setDate(endDate.getDate() - 1); // -1 to make it inclusive
        break;
    }

    // Ensure end date is always on Sunday
    return adjustToEndOfWeekSunday(endDate);
  };

  const calculateDurationFromDates = (startDate: Date, endDate: Date): { weeks: number, days: number, months: number } => {
    const diffTime = endDate.getTime() - startDate.getTime();
    const days = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to make it inclusive
    const weeks = Math.round(days / 7);
    const months = Math.round(days / 30.44); // Average days per month

    return { weeks, days, months };
  };

  // Payment calculation helper function
  const calculatePaymentFees = (course: Course, newStartDate: Date, newEndDate: Date, newTotalTuitionFee?: number) => {
    if (!includePaymentCalculation || !course.totalTuitionFee) {
      return {};
    }

    const {
      totalMaterialFee = 0,
      totalPlacementFee = 0,
      prepaymentTotal = 0,
    } = course;

    // Use the new total tuition fee if provided, otherwise use the original
    const totalTuitionFee = newTotalTuitionFee || course.totalTuitionFee || 0;

    // Calculate number of days and months
    const numberOfDays = Math.ceil((newEndDate.getTime() - newStartDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    // Basic calculations
    const totalAllFees = totalMaterialFee + totalPlacementFee + totalTuitionFee;
    const prepaidAmount = prepaymentTotal;

    // Fee allocation priority: Tuition → Material → Placement
    const prepaidTuitionFee = Math.min(prepaidAmount, totalTuitionFee);

    const prepaidMaterialFee = prepaidAmount > totalTuitionFee
      ? Math.min(prepaidAmount - totalTuitionFee, totalMaterialFee)
      : 0;

    const prepaidPlacementFee = prepaidAmount > (totalTuitionFee + totalMaterialFee)
      ? Math.min(prepaidAmount - totalTuitionFee - totalMaterialFee, totalPlacementFee)
      : 0;

    const otherPrePaidNonTuitionFee = prepaidMaterialFee + prepaidPlacementFee;

    // Coverage period calculation
    let coverageStartDate = "";
    let coverageEndDate = "";

    if (totalTuitionFee > 0) {
      const coverageRatio = prepaidTuitionFee / totalTuitionFee;

      // If coverage is 100% or more, use the full end date
      if (coverageRatio >= 1) {
        coverageStartDate = format(newStartDate, "dd/MM/yyyy");
        coverageEndDate = format(newEndDate, "dd/MM/yyyy");
      } else {
        const coveredDays = Math.floor(coverageRatio * numberOfDays);
        const endCoverageDate = addDays(newStartDate, coveredDays - 1);

        coverageStartDate = format(newStartDate, "dd/MM/yyyy");
        coverageEndDate = format(endCoverageDate, "dd/MM/yyyy");
      }
    }

    return {
      totalAllFees,
      prepaidAmount,
      prepaidTuitionFee,
      prepaidMaterialFee,
      prepaidPlacementFee,
      otherPrePaidNonTuitionFee,
      coverageStartDate,
      coverageEndDate,
    };
  };

  return (
    <div className="min-h-screen py-8 px-4 bg-gradient-to-br from-slate-50 to-blue-50 w-full">
      <div className="max-w-5xl mx-auto">
        <div className="text-center mb-10">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl shadow-lg">
              <Calculator className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              Student Deferment Calculator
            </h1>
          </div>
          <p className="max-w-2xl mx-auto text-lg text-slate-600 leading-relaxed">
            Calculate new course dates after deferment periods and plan
            sequential courses (up to 4).
          </p>
        </div>

        {/* Advanced Mode Toggle */}
        <Card className="shadow-xl bg-white border-0 rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300 mb-6">
          <CardContent className="p-6">
            <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border border-slate-200">
              <div>
                <label className="block text-sm font-semibold text-slate-700 mb-1">
                  Calculator Mode
                </label>
                <p className="text-xs text-slate-600">
                  Enable advanced mode for payment plan calculations and fee allocation
                </p>
              </div>
              <div className="flex items-center gap-3">
                <span className={`text-sm font-medium transition-colors ${!includePaymentCalculation ? 'text-slate-900' : 'text-slate-500'}`}>
                  Basic
                </span>
                <Switch
                  checked={includePaymentCalculation}
                  onCheckedChange={setIncludePaymentCalculation}
                />
                <span className={`text-sm font-medium transition-colors ${includePaymentCalculation ? 'text-slate-900' : 'text-slate-500'}`}>
                  Advanced
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Plan Calculator - Only visible in Advanced Mode */}
        {includePaymentCalculation && (
          <Card className="shadow-xl bg-white border-0 rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300 mb-6">
            <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-100">
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-md">
                  <DollarSign className="w-5 h-5 text-white" />
                </div>
                <div>
                  <span className="text-xl font-bold text-slate-800">Payment Plan Calculator</span>
                  <p className="text-sm text-slate-600 font-normal mt-1">
                    (input same details as per the finance AR fee check)
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2 text-green-700">
                  <CheckCircle2 className="w-5 h-5" />
                  <span className="text-sm font-medium">
                    Advanced mode enabled! Fee allocation fields will appear in the first course section.
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}



        <div className="space-y-4">
          {courses.map((course, index) => (
            <Card
              key={course.id}
              className="shadow-xl bg-white border-0 rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-1"
            >
              <CardHeader className="flex flex-row items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-md">
                    <BookOpen className="w-5 h-5 text-white" />
                  </div>
                  <span className="text-xl font-bold text-slate-800">
                    Course {course.id}
                  </span>
                </CardTitle>
                {index > 0 && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeCourse(course.id)}
                  >
                    <Trash2 className="w-5 h-5 text-red-500" />
                  </Button>
                )}
              </CardHeader>
              <CardContent className="space-y-4 p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="col-span-full">
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Course Name
                    </label>
                    <CourseSearchInput
                      value={course.courseName}
                      onChange={(value) =>
                        handleCourseChange(course.id, "courseName", value)
                      }
                      placeholder="Search for courses... e.g., Diploma of IT, Child Care, Business"
                      className="w-full"
                    />
                    {errors[course.id]?.courseName && (
                      <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors[course.id]?.courseName}
                      </p>
                    )}
                  </div>
                </div>
                {/* Row 1: Original Start Date | Duration | Original End Date */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
                  <div>
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Original Start Date
                    </label>
                    <DatePicker
                      value={course.originalStartDate}
                      onChange={(date) =>
                        handleCourseChange(course.id, "originalStartDate", date)
                      }
                      className="w-48"
                    />
                    {errors[course.id]?.originalStartDate && (
                      <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors[course.id]?.originalStartDate}
                      </p>
                    )}
                  </div>

                  <div className="flex justify-center">
                    <div>
                      <label className="block text-sm font-semibold text-slate-700 mb-3 text-center">
                        Duration
                      </label>
                      <div className="space-y-2">
                        <div className="flex gap-1 justify-center">
                          <Input
                            type="number"
                            value={
                              course.durationType === 'days' ? (course.durationDays || '') :
                              course.durationType === 'months' ? (course.durationMonths || '') :
                              (course.durationWeeks || '')
                            }
                            onChange={(e) => {
                              const value = parseInt(e.target.value);
                              const type = (course.durationType || 'weeks') as 'weeks' | 'days' | 'months';
                              if (value && value > 0) {
                                handleDurationChange(course.id, value, type);
                              } else if (e.target.value === '') {
                                // Clear the field
                                const field = type === 'days' ? 'durationDays' :
                                             type === 'months' ? 'durationMonths' : 'durationWeeks';
                                handleCourseChange(course.id, field, 0);
                              }
                            }}
                            placeholder="0"
                            className="w-20 text-sm text-center"
                          />
                          <select
                            value={course.durationType || 'weeks'}
                            onChange={(e) => {
                              const newType = e.target.value as 'weeks' | 'days' | 'months';
                              handleCourseChange(course.id, "durationType", newType);
                            }}
                            className="w-20 px-2 py-2 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="weeks">wks</option>
                            <option value="days">days</option>
                            <option value="months">mos</option>
                          </select>
                        </div>
                        {course.originalEndDate && course.durationWeeks && (
                          <p className="text-xs text-gray-500 text-center">
                            {course.durationWeeks}w ({course.durationDays}d)
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Original End Date
                    </label>
                    <DatePicker
                      value={course.originalEndDate}
                      onChange={(date) => handleEndDateChange(course.id, date)}
                      className="w-48"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Auto-calculated or manual
                    </p>
                    {errors[course.id]?.originalEndDate && (
                      <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors[course.id]?.originalEndDate}
                      </p>
                    )}
                  </div>
                </div>

                {/* Row 2: Deferment Start Date | (empty) | Resumption Date */}
                {index === 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
                    <div>
                      <label className="block text-sm font-semibold text-slate-700 mb-3">
                        Deferment Start Date
                      </label>
                      <DatePicker
                        value={course.defermentStartDate}
                        onChange={(date) =>
                          handleCourseChange(
                            course.id,
                            "defermentStartDate",
                            date
                          )
                        }
                        className="w-48"
                      />
                      {errors[course.id]?.defermentStartDate && (
                        <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                          <AlertCircle className="w-4 h-4" />
                          {errors[course.id]?.defermentStartDate}
                        </p>
                      )}
                    </div>

                    {/* Empty middle column for alignment */}
                    <div></div>

                    <div>
                      <label className="block text-sm font-semibold text-slate-700 mb-3">
                        Resumption Date
                      </label>
                      <DatePicker
                        value={course.resumptionDate}
                        onChange={(date) =>
                          handleCourseChange(course.id, "resumptionDate", date)
                        }
                        className="w-48"
                      />
                      {errors[course.id]?.resumptionDate && (
                        <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                          <AlertCircle className="w-4 h-4" />
                          {errors[course.id]?.resumptionDate}
                        </p>
                      )}
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <label className="block text-sm font-semibold text-slate-700">
                        New Start Date
                      </label>
                      <div className="group relative">
                        <Info className="w-4 h-4 text-blue-500 cursor-help" />
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                          💡 Check intake dates before setting the new start
                          date
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => window.open("/intake-dates", "_blank")}
                        className="text-xs px-2 py-1 h-6"
                      >
                        <ExternalLink className="w-3 h-3 mr-1" />
                        View Intake Dates
                      </Button>
                    </div>
                    <DatePicker
                      value={course.newStartDate}
                      onChange={(date) =>
                        handleCourseChange(course.id, "newStartDate", date)
                      }
                      className="w-48"
                    />
                    {errors[course.id]?.newStartDate && (
                      <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors[course.id]?.newStartDate}
                      </p>
                    )}
                  </div>
                )}

                {/* Payment Plan and Fee Allocation Fields */}
                {includePaymentCalculation && (
                  <div className="border-t border-gray-200 pt-6 mt-6">
                    <div className="flex items-center gap-2 mb-4">
                      <DollarSign className="w-5 h-5 text-green-600" />
                      <div>
                        <h4 className="font-bold text-lg text-slate-800">Fee Allocation as per AR Fee Check</h4>
                        <p className="text-sm text-slate-600 font-normal">
                          input all the same details from AR fee check
                        </p>
                      </div>
                    </div>



                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-semibold text-slate-700 mb-3">
                          Total tuition fee as per fee check ($) *
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={course.totalTuitionFee || ""}
                          onChange={(e) => handleCourseChange(course.id, "totalTuitionFee", parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                          className="w-full bg-blue-50 border-blue-200"
                        />
                        {errors[course.id]?.totalTuitionFee && (
                          <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                            <AlertCircle className="w-4 h-4" />
                            {errors[course.id]?.totalTuitionFee}
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-slate-700 mb-3">
                          Tuition fees paid until now ($) *
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={course.tuitionFeesPaidSoFar || ""}
                          onChange={(e) => handleCourseChange(course.id, "tuitionFeesPaidSoFar", parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                          className="w-full bg-blue-50 border-blue-200"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-slate-700 mb-3">
                          Unspent tuition fees ($) *
                          <span className="text-xs text-slate-500 font-normal block mt-1">
                            (Use negative values if student owes money)
                          </span>
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={course.prepaymentTotal || ""}
                          onChange={(e) => handleCourseChange(course.id, "prepaymentTotal", parseFloat(e.target.value) || 0)}
                          placeholder="0.00 (can be negative)"
                          className="w-full bg-blue-50 border-blue-200"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <label className="block text-sm font-semibold text-slate-700 mb-3">
                          Total Material Fee ($)
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={course.totalMaterialFee || ""}
                          onChange={(e) => handleCourseChange(course.id, "totalMaterialFee", parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                          className="w-full bg-blue-50 border-blue-200"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-slate-700 mb-3">
                          Total Placement Fee ($)
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={course.totalPlacementFee || ""}
                          onChange={(e) => handleCourseChange(course.id, "totalPlacementFee", parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                          className="w-full bg-blue-50 border-blue-200"
                        />
                      </div>
                    </div>
                  </div>
                )}



                {course.newEndDate && (
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 p-6 rounded-xl mt-6">
                    <div className="flex items-center gap-2 mb-4">
                      <CheckCircle2 className="w-6 h-6 text-green-600" />
                      <h4 className="font-bold text-lg text-green-800">
                        Calculation Results for {course.courseName || `Course ${course.id}`}
                      </h4>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center gap-2 p-3 bg-white rounded-lg border border-green-200">
                        <span className="font-semibold text-slate-700">
                          Duration:
                        </span>
                        <span className="text-green-700 font-bold">
                          {course.duration} weeks
                        </span>
                      </div>
                      {course.id === 1 && (
                        <div className="flex items-center gap-2 p-3 bg-white rounded-lg border border-green-200">
                          <span className="font-semibold text-slate-700">
                            Deferment:
                          </span>
                          <span className="text-green-700 font-bold">
                            {course.defermentDuration} weeks
                          </span>
                        </div>
                      )}
                      <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-green-200">
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-slate-700">
                            New Start Date:
                          </span>
                          <span className="text-green-700 font-bold">
                            {course.newCalculatedStartDate}
                          </span>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            copyToClipboard(course.newCalculatedStartDate)
                          }
                          className="h-6 w-6 p-0 hover:bg-green-100"
                        >
                          <Copy className="w-3 h-3 text-green-600" />
                        </Button>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-green-200">
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-slate-700">
                            New End Date:
                          </span>
                          <span className="text-green-700 font-bold">
                            {course.newEndDate}
                          </span>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(course.newEndDate)}
                          className="h-6 w-6 p-0 hover:bg-green-100"
                        >
                          <Copy className="w-3 h-3 text-green-600" />
                        </Button>
                      </div>

                      {/* Payment Results Section */}
                      {includePaymentCalculation && course.totalAllFees && (
                        <>
                          <div className="col-span-full">
                            <div className="flex items-center gap-2 my-4">
                              <DollarSign className="w-5 h-5 text-green-600" />
                              <h5 className="font-bold text-md text-green-800">Payment Plan Results</h5>
                            </div>
                          </div>

                          {/* New Total Tuition Fee with A-B+C Formula */}
                          <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <span className="font-semibold text-slate-700">New Total Tuition Fee:</span>
                                <span className="text-red-700 font-bold">
                                  {formatCurrency((course.totalTuitionFee || 0) - (course.tuitionFeesPaidSoFar || 0) + (course.prepaymentTotal || 0))}
                                </span>
                                {course.totalTuitionFee && (course.tuitionFeesPaidSoFar !== undefined || course.prepaymentTotal !== undefined) && (
                                  <span className="text-xs text-slate-500">(A - B + C)</span>
                                )}
                              </div>
                              <div className="flex items-center gap-2">
                                {course.totalTuitionFee && (course.tuitionFeesPaidSoFar !== undefined || course.prepaymentTotal !== undefined) && (
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      const element = document.getElementById(`formula-${course.id}`);
                                      if (element) {
                                        element.style.display = element.style.display === 'none' ? 'block' : 'none';
                                      }
                                    }}
                                    className="h-6 w-6 p-0 hover:bg-red-100"
                                  >
                                    <Info className="w-3 h-3 text-red-600" />
                                  </Button>
                                )}
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(((course.totalTuitionFee || 0) - (course.tuitionFeesPaidSoFar || 0) + (course.prepaymentTotal || 0)).toString())}
                                  className="h-6 w-6 p-0 hover:bg-red-100"
                                >
                                  <Copy className="w-3 h-3 text-red-600" />
                                </Button>
                              </div>
                            </div>

                            {/* Important condition warning when unspent fees are negative */}
                            {(course.prepaymentTotal || 0) < 0 && (
                              <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-lg mb-2">
                                <div className="flex items-center gap-2">
                                  <div className="w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center">
                                    <span className="text-xs font-bold text-yellow-800">!</span>
                                  </div>
                                  <span className="text-sm font-medium text-yellow-800">Important:</span>
                                </div>
                                <p className="text-xs text-yellow-700 mt-1">
                                  This amount is only valid <strong>after</strong> the student pays the outstanding {formatCurrency(Math.abs(course.prepaymentTotal || 0))} to make unspent tuition fees = $0.00
                                </p>
                              </div>
                            )}

                            {/* Collapsible calculation formula */}
                            {course.totalTuitionFee && (course.tuitionFeesPaidSoFar !== undefined || course.prepaymentTotal !== undefined) && (
                              <div id={`formula-${course.id}`} style={{ display: 'none' }} className="text-sm text-red-800 space-y-1 mt-2 pt-2 border-t border-red-200">
                                <div className="flex items-center gap-2">
                                  <Calculator className="w-4 h-4 text-red-600" />
                                  <span className="font-medium">Formula: A - B + C</span>
                                </div>
                                <div className="space-y-1 text-xs">
                                  <div>A = Total tuition fee as per fee check: {formatCurrency(course.totalTuitionFee || 0)}</div>
                                  <div>B = Tuition fees paid until now: {formatCurrency(course.tuitionFeesPaidSoFar || 0)}</div>
                                  <div>C = Unspent tuition fees: {formatCurrency(course.prepaymentTotal || 0)}</div>
                                </div>
                                <div className="font-mono bg-white p-2 rounded border text-xs">
                                  {formatCurrency(course.totalTuitionFee || 0)} - {formatCurrency(course.tuitionFeesPaidSoFar || 0)} + {formatCurrency(course.prepaymentTotal || 0)} = <strong>{formatCurrency((course.totalTuitionFee || 0) - (course.tuitionFeesPaidSoFar || 0) + (course.prepaymentTotal || 0))}</strong>
                                </div>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                            <div className="flex items-center gap-2">
                              <span className="font-semibold text-slate-700">Initial Deposit:</span>
                              <span className="text-red-700 font-bold">{formatCurrency(course.prepaidAmount || 0)}</span>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(course.prepaidAmount?.toString() || "")}
                              className="h-6 w-6 p-0 hover:bg-red-100"
                            >
                              <Copy className="w-3 h-3 text-red-600" />
                            </Button>
                          </div>

                          <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                            <div className="flex items-center gap-2">
                              <span className="font-semibold text-slate-700">Prepaid Material Fee:</span>
                              <span className="text-red-700 font-bold">{formatCurrency(course.prepaidMaterialFee || 0)}</span>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(course.prepaidMaterialFee?.toString() || "")}
                              className="h-6 w-6 p-0 hover:bg-red-100"
                            >
                              <Copy className="w-3 h-3 text-red-600" />
                            </Button>
                          </div>

                          <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                            <div className="flex items-center gap-2">
                              <span className="font-semibold text-slate-700">Prepaid Placement Fee:</span>
                              <span className="text-red-700 font-bold">{formatCurrency(course.prepaidPlacementFee || 0)}</span>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(course.prepaidPlacementFee?.toString() || "")}
                              className="h-6 w-6 p-0 hover:bg-red-100"
                            >
                              <Copy className="w-3 h-3 text-red-600" />
                            </Button>
                          </div>

                          <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                            <div className="flex items-center gap-2">
                              <span className="font-semibold text-slate-700">Other Pre-Paid Non Tuition Fee:</span>
                              <span className="text-red-700 font-bold">{formatCurrency(course.otherPrePaidNonTuitionFee || 0)}</span>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(course.otherPrePaidNonTuitionFee?.toString() || "")}
                              className="h-6 w-6 p-0 hover:bg-red-100"
                            >
                              <Copy className="w-3 h-3 text-red-600" />
                            </Button>
                          </div>

                          {course.coverageStartDate && course.coverageEndDate && (
                            <div className="col-span-full">
                              <div className="bg-purple-50 border border-purple-200 p-4 rounded-lg">
                                <div className="flex items-center gap-2 mb-2">
                                  <Calendar className="w-4 h-4 text-purple-600" />
                                  <span className="font-semibold text-purple-700">Coverage Period by Prepaid Tuition Fee:</span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-4">
                                    <div>
                                      <span className="text-sm font-medium text-purple-700">Start:</span>
                                      <div className="font-bold text-purple-800">{course.coverageStartDate}</div>
                                    </div>
                                    <div className="text-purple-500">→</div>
                                    <div>
                                      <span className="text-sm font-medium text-purple-700">End:</span>
                                      <div className="font-bold text-purple-800">{course.coverageEndDate}</div>
                                    </div>
                                  </div>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => copyToClipboard(`${course.coverageStartDate} - ${course.coverageEndDate}`)}
                                    className="h-6 w-6 p-0 hover:bg-purple-100"
                                  >
                                    <Copy className="w-3 h-3 text-purple-600" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      )}

                      {/* Fee Allocation Results Section - Integrated with Payment Plan */}
                      {includePaymentCalculation && index === 0 && course.feeAllocationResult && (
                        <>


                          {/* Status Banner */}
                          <div className={`col-span-full p-4 rounded-lg border-l-4 ${
                            course.feeAllocationResult.canProceedWithDeferment
                              ? 'bg-green-50 border-green-500 text-green-800'
                              : 'bg-red-50 border-red-500 text-red-800'
                          }`}>
                            <div className="flex items-center gap-2 font-medium mb-2">
                              {course.feeAllocationResult.canProceedWithDeferment ? (
                                <CheckCircle2 className="w-5 h-5" />
                              ) : (
                                <AlertCircle className="w-5 h-5" />
                              )}
                              {course.feeAllocationResult.canProceedWithDeferment ? 'Approved for Deferment' : 'Payment Required'}
                            </div>
                            <p className="text-sm">{course.feeAllocationResult.statusMessage}</p>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}

          {courses.length < 4 && (
            <Button
              onClick={addCourse}
              className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
            >
              <Plus className="mr-3 h-5 w-5" /> Add Another Course
            </Button>
          )}
        </div>

        {/* Save Status */}
        {lastSaved && (
          <div className="mt-6 text-center">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 border border-green-200 rounded-lg">
              <Save className="w-4 h-4 text-green-600" />
              <span className="text-sm text-green-700 font-medium">
                Auto-saved: {lastSaved}
              </span>
            </div>
          </div>
        )}



        <div className="mt-10 flex flex-col sm:flex-row justify-center items-center gap-4">
          <Button
            onClick={handleCalculate}
            disabled={isCalculating}
            size="lg"
            className="px-12 py-4 text-xl font-bold bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {isCalculating ? (
              <>
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3" />
                Calculating...
              </>
            ) : (
              <>
                <Calculator className="mr-3 h-6 w-6" />
                Calculate All Courses
              </>
            )}
          </Button>

          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
            <Button
              onClick={clearAllData}
              variant="outline"
              size="sm"
              className="px-4 py-2 text-sm font-medium border border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Clear All Data
            </Button>

            <Button
              onClick={saveToDatabase}
              variant="outline"
              size="sm"
              className="px-4 py-2 text-sm font-medium border border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
              disabled={!courses.some((course) => course.newEndDate) || isSavingToDatabase}
            >
              <Save className="mr-2 h-4 w-4" />
              {isSavingToDatabase ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>

        {/* Calculation History Section */}
        {calculationHistory.length > 0 && (
          <div className="mt-12">
            <Card className="shadow-xl bg-white border-0 rounded-2xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 border-b border-purple-100">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-md">
                      <History className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-xl font-bold text-slate-800">
                      Calculation History
                    </span>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => setShowHistory(!showHistory)}
                      variant="ghost"
                      size="sm"
                      className="text-purple-600 hover:bg-purple-100"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      {showHistory ? "Hide" : "Show"} (
                      {calculationHistory.length})
                    </Button>
                    <Button
                      onClick={clearHistory}
                      variant="ghost"
                      size="sm"
                      className="text-red-600 hover:bg-red-100"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>

              {showHistory && (
                <CardContent className="p-0">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50 border-b border-gray-200">
                        <tr>
                          <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Calculation Name
                          </th>
                          <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Date Saved
                          </th>
                          <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Courses
                          </th>
                          <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Total Deferment
                          </th>
                          <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Summary
                          </th>
                          <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {calculationHistory.map((item) => (
                          <tr
                            key={item.id}
                            className="hover:bg-gray-50 transition-colors"
                          >
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="font-medium text-gray-900">
                                {item.title}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-600">
                                {item.timestamp}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {item.courses.length} course
                                {item.courses.length !== 1 ? "s" : ""}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-blue-600">
                                {item.courses.find((c) => c.id === 1)
                                  ?.defermentDuration || 0}{" "}
                                weeks
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              <div className="text-sm text-gray-600 max-w-xs">
                                {item.courses.map((course) => (
                                  <div key={course.id} className="mb-1">
                                    <span className="font-medium">
                                      {course.courseName ||
                                        `Course ${course.id}`}
                                      :
                                    </span>
                                    <br />
                                    <span className="text-xs text-gray-500">
                                      {course.newCalculatedStartDate} →{" "}
                                      {course.newEndDate}
                                      {course.id === 1 &&
                                        ` (${course.defermentDuration}w defer)`}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                              <div className="flex items-center justify-center gap-2">
                                <Button
                                  onClick={() => loadFromHistory(item)}
                                  size="sm"
                                  className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 text-xs"
                                >
                                  <Eye className="w-3 h-3 mr-1" />
                                  Load
                                </Button>
                                <Button
                                  onClick={() => {
                                    const updatedHistory =
                                      calculationHistory.filter(
                                        (h) => h.id !== item.id
                                      );
                                    setCalculationHistory(updatedHistory);
                                    localStorage.setItem(
                                      HISTORY_KEY,
                                      JSON.stringify(updatedHistory)
                                    );
                                  }}
                                  variant="ghost"
                                  size="sm"
                                  className="text-red-600 hover:bg-red-50 px-2 py-1 text-xs"
                                >
                                  <Trash2 className="w-3 h-3" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {calculationHistory.length === 0 && (
                    <div className="text-center py-12 text-gray-500">
                      <History className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                      <p className="text-lg font-medium">
                        No saved calculations yet
                      </p>
                      <p className="text-sm">
                        Complete a calculation to see it saved here
                      </p>
                    </div>
                  )}
                </CardContent>
              )}
            </Card>
          </div>
        )}


      </div>
    </div>
  );
}
