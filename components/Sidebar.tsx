'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Calculator,
  Calendar,
  Menu,
  X,
  GraduationCap,
  ChevronRight,
  DollarSign,
  LogOut,
  User,
  Activity,
  Sparkles,
  Database,
} from "lucide-react";
import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { AuthUser } from "@/lib/supabase/auth";

export default function Sidebar() {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const { isAuthenticated, user, logout } = useAuth();

  const toolCategories = [
    {
      name: "Academic Tools",
      tools: [
        {
          name: "Deferment Calculator",
          href: "/deferment-calculator",
          icon: Calculator,
          description: "Calculate course deferrals and adjustments",
        },
        {
          name: "Fee Calculator",
          href: "/fee-calculator",
          icon: DollarSign,
          description: "Calculate payment coverage and allocations",
        },
        {
          name: "Saved Calculations",
          href: "/admin/saved-calculations",
          icon: Database,
          description: "View and manage saved deferment calculations",
        },
      ],
    },
    {
      name: "Course Information",
      tools: [
        {
          name: "Intake Dates",
          href: "/intake-dates",
          icon: Calendar,
          description: "View available course start dates",
        },
      ],
    },
    // Temporarily disabled - Data Management section
    // {
    //   name: "Data Management",
    //   tools: [
    //     {
    //       name: "Google Sheets Import",
    //       href: "/admin/import-sheets",
    //       icon: FileSpreadsheet,
    //       description: "Import clean course data from Google Sheets",
    //     },
    //     {
    //       name: "Database Sync",
    //       href: "/admin/sync",
    //       icon: Database,
    //       description: "Sync JSON data to database",
    //     },
    //   ],
    // },
  ];

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  const closeSidebar = () => {
    setIsOpen(false);
  };

  return (
    <>
      {/* Mobile menu button with modern styling */}
      <button
        onClick={toggleSidebar}
        className="lg:hidden fixed top-4 left-4 z-50 group"
      >
        <div className="p-3 bg-white/90 backdrop-blur-md border border-gray-200/80 text-gray-700 rounded-xl hover:bg-white hover:border-blue-200 transition-all duration-300 shadow-lg hover:shadow-xl">
          {isOpen ? (
            <X
              size={20}
              className="transition-all duration-300 group-hover:rotate-90"
            />
          ) : (
            <Menu
              size={20}
              className="transition-all duration-300 group-hover:scale-110"
            />
          )}
        </div>
      </button>

      {/* Enhanced overlay for mobile */}
      {isOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/40 backdrop-blur-sm z-40 transition-all duration-300"
          onClick={closeSidebar}
        />
      )}

      {/* Modern sidebar with glass morphism */}
      <aside
        className={`
        fixed lg:static lg:translate-x-0 inset-y-0 left-0 z-50
        w-80 bg-white/95 backdrop-blur-xl border-r border-gray-200/80 shadow-2xl flex flex-col
        transition-all duration-500 ease-out
        ${isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"}
      `}
        style={{
          background:
            "linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%)",
        }}
      >
        {/* Enhanced header with modern branding */}
        <div className="p-4 border-b border-gray-200/60">
          <div className="flex items-center gap-3">
            {/* Modern logo with gradient */}
            <div className="relative w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-300">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <GraduationCap className="w-6 h-6 text-white relative z-10" />
              <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            {/* Enhanced brand text */}
            <div className="flex-1">
              <h1 className="text-lg font-bold text-gray-900 leading-tight tracking-tight">
                EduPace
              </h1>
              <div className="flex items-center gap-2 mt-0.5">
                <p className="text-sm text-gray-600 font-medium">
                  Educational Platform
                </p>
                <div className="flex items-center gap-1">
                  <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-green-600 font-medium">
                    Live
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced navigation with modern styling */}
        <nav className="flex-1 p-4 overflow-y-auto">
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-3">
              <Sparkles className="w-4 h-4 text-blue-600" />
              <h2 className="text-sm font-semibold text-gray-700 tracking-wide">

              </h2>
            </div>
          </div>

          <div className="space-y-6">
            {toolCategories.map((category) => (
              <div key={category.name} className="space-y-2">
                <div className="flex items-center gap-2 px-1">
                  <div className="w-1.5 h-1.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
                  <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    {category.name}
                  </h3>
                </div>

                <ul className="space-y-1">
                  {category.tools.map((tool) => {
                    const isActive = pathname === tool.href;
                    const Icon = tool.icon;

                    return (
                      <li key={tool.name}>
                        <Link
                          href={tool.href}
                          onClick={closeSidebar}
                          className={`
                            group relative block px-4 py-4 rounded-2xl transition-all duration-300
                            transform hover:scale-[1.02] hover:-translate-y-0.5
                            ${
                              isActive
                                ? "bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 shadow-lg border border-blue-200/60"
                                : "text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50/50 hover:text-blue-600 hover:shadow-md"
                            }
                          `}
                        >
                          {isActive && (
                            <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-blue-500 to-purple-500 rounded-r-full"></div>
                          )}

                          <div className="flex items-center space-x-4">
                            <div
                              className={`
                                relative p-2.5 rounded-xl transition-all duration-300
                                ${
                                  isActive
                                    ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg"
                                    : "bg-gray-100 text-gray-600 group-hover:bg-gradient-to-r group-hover:from-blue-500 group-hover:to-indigo-600 group-hover:text-white group-hover:shadow-lg"
                                }
                              `}
                            >
                              <Icon size={18} />
                              {isActive && (
                                <div className="absolute inset-0 bg-white/20 rounded-xl"></div>
                              )}
                            </div>

                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <span
                                  className={`
                                    text-sm font-semibold transition-colors duration-300 truncate
                                    ${
                                      isActive
                                        ? "text-blue-800"
                                        : "text-gray-900 group-hover:text-blue-700"
                                    }
                                  `}
                                >
                                  {tool.name}
                                </span>
                                <ChevronRight
                                  size={16}
                                  className={`
                                    flex-shrink-0 ml-2 transition-all duration-300
                                    ${
                                      isActive
                                        ? "text-blue-600 rotate-90 scale-110"
                                        : "text-gray-400 group-hover:text-blue-500 group-hover:translate-x-1"
                                    }
                                  `}
                                />
                              </div>
                              <p
                                className={`
                                  text-xs transition-colors duration-300 mt-1 leading-relaxed
                                  ${
                                    isActive
                                      ? "text-blue-600"
                                      : "text-gray-500 group-hover:text-blue-500"
                                  }
                                `}
                              >
                                {tool.description}
                              </p>
                            </div>
                          </div>
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            ))}
          </div>
        </nav>

        {/* Enhanced footer with modern styling */}
        <div className="p-3 border-t border-gray-200/60 bg-gradient-to-r from-gray-50/50 to-blue-50/30">
          {isAuthenticated && user ? (
            <>
              {/* Enhanced User Information */}
              <div className="flex items-center gap-3 mb-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200/40">
                <div className="relative w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center shadow-md">
                  <User className="w-4 h-4 text-white" />
                  <div className="absolute inset-0 bg-white/20 rounded-lg"></div>
                </div>
                <div className="flex-1">
                  <p className="text-xs font-semibold text-gray-900">
                    {user.email}
                  </p>
                  <div className="flex items-center gap-1 mt-0.5">
                    <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse"></div>
                    <p className="text-xs text-green-600 font-medium">
                      Authenticated
                    </p>
                  </div>
                </div>
              </div>

              {/* Enhanced Logout Button */}
              <button
                onClick={logout}
                className="w-full flex items-center justify-center gap-2 px-3 py-2 text-xs font-semibold text-red-700 bg-gradient-to-r from-red-50 to-rose-50 hover:from-red-100 hover:to-rose-100 rounded-xl transition-all duration-300 mb-3 border border-red-200/40 hover:border-red-300/60 hover:shadow-md transform hover:scale-[1.01]"
              >
                <LogOut className="w-3 h-3" />
                Sign Out
              </button>
            </>
          ) : null}

          {/* Enhanced System Status */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <div className="relative">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                  <div className="absolute inset-0 w-1.5 h-1.5 bg-green-400 rounded-full animate-ping"></div>
                </div>
                <span className="text-xs font-semibold text-gray-700">
                  System Online
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Activity className="w-3 h-3 text-blue-500" />
                <span className="text-xs text-gray-500 font-medium">v2.0</span>
              </div>
            </div>

            <div className="pt-2 border-t border-gray-200/50">
              <p className="text-xs text-gray-500 leading-tight text-center">
                <span className="font-semibold text-gray-600">
                  EduPace
                </span>
                <br />
                <span className="text-gray-400">© 2025 Academic Services</span>
              </p>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
}
