"use client";

import DefermentCalculatorEnhanced from "@/components/DefermentCalculatorEnhanced";
import ProtectedRoute from "@/components/ProtectedRoute";
import Sidebar from "@/components/Sidebar";

export default function DefermentCalculatorPage() {
  return (
    <ProtectedRoute>
      <div className="flex h-screen overflow-hidden bg-white">
        <Sidebar />
        <main className="flex-1 overflow-auto lg:ml-0 relative bg-white">
          <div className="min-h-full pt-20 lg:pt-0">
            <div className="animate-fade-in-up">
              <DefermentCalculatorEnhanced />
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
