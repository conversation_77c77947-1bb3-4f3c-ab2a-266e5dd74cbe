'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import ProtectedRoute from "@/components/ProtectedRoute";
import Sidebar from "@/components/Sidebar";
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';

interface ImportResult {
  success: boolean;
  message: string;
  details?: {
    sheets: string[];
    totalCourses: number;
    skippedCourses: number;
    errors: string[];
  };
}

export default function GoogleSheetsImportPage() {
  const [isImporting, setIsImporting] = useState(false);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [clearCorruptedData, setClearCorruptedData] = useState(true);

  const handleImport = async () => {
    setIsImporting(true);
    setResult(null);

    try {
      const response = await fetch('/api/google-sheets-import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ clearCorruptedData }),
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        message: 'Failed to import data: ' + (error instanceof Error ? error.message : 'Unknown error'),
      });
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <ProtectedRoute>
      <div className="flex h-screen overflow-hidden bg-white">
        <Sidebar />
        <main className="flex-1 overflow-auto lg:ml-0 relative bg-white">
          <div className="min-h-full pt-20 lg:pt-0">
            <div className="animate-fade-in-up">
              <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Google Sheets Import</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Import Course Data from Google Sheets</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">What This Does:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Automatically connects to your Google Sheets &quot;Courses Database&quot;</li>
                <li>Reads all college sheets: AIBT, REACH, NPA, AVT, AAIBT, IBIC</li>
                <li>Imports clean course data with valid CRICOS codes</li>
                <li>Optionally clears corrupted data before import</li>
              </ul>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="clearCorrupted"
                checked={clearCorruptedData}
                onChange={(e) => setClearCorruptedData(e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded border-gray-300"
              />
              <label htmlFor="clearCorrupted" className="text-sm font-medium">
                Clear corrupted data (fake CRICOS codes) before import
              </label>
            </div>

            <Button
              onClick={handleImport}
              disabled={isImporting}
              className="w-full"
            >
              {isImporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Importing from Google Sheets...
                </>
              ) : (
                'Import from Google Sheets'
              )}
            </Button>
          </div>

          {result && (
            <Alert className={result.success ? 'border-green-500' : 'border-red-500'}>
              <div className="flex items-center">
                {result.success ? (
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500 mr-2" />
                )}
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium">{result.message}</p>
                    {result.details && (
                      <div className="text-sm space-y-1">
                        <p>Sheets processed: {result.details.sheets.join(', ')}</p>
                        <p>Total courses imported: {result.details.totalCourses}</p>
                        {result.details.skippedCourses > 0 && (
                          <p>Courses skipped: {result.details.skippedCourses}</p>
                        )}
                        {result.details.errors.length > 0 && (
                          <div>
                            <p className="font-medium">Errors:</p>
                            <ul className="list-disc list-inside">
                              {result.details.errors.map((error, index) => (
                                <li key={index}>{error}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </AlertDescription>
              </div>
            </Alert>
          )}

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">Next Steps:</h3>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              <li>Import clean course data using this page</li>
              <li>Go to <strong>/admin/sync</strong> to sync intake dates</li>
              <li>Your calculator will now work with clean data</li>
            </ol>
          </div>
        </CardContent>
      </Card>
              </div>
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
