"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import ProtectedRoute from "@/components/ProtectedRoute";
import Sidebar from "@/components/Sidebar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  CheckCircle,
  AlertCircle,
  Loader2,
  FileSpreadsheet,
  Database,
  Trash2,
} from "lucide-react";

export default function ImportSheetsPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [sheetsData, setSheetsData] = useState<any>({});
  const [clearExisting, setClearExisting] = useState(false);

  const handleSheetsDataInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = event.target.value.trim();
    if (!value) {
      setSheetsData({});
      return;
    }

    try {
      const parsed = JSON.parse(value);
      if (typeof parsed === 'object' && parsed !== null) {
        setSheetsData(parsed);
        setError(null);
      } else {
        setError("Data must be an object with sheet names as keys");
      }
    } catch (err) {
      setError("Invalid JSON format");
    }
  };

  const handleImport = async () => {
    if (!sheetsData || Object.keys(sheetsData).length === 0) {
      setError("Please provide Google Sheets data first");
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch("/api/import-sheets", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          sheetsData,
          clearExisting
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Import failed");
      }

      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const sampleData = `{
  "AWT": [
    {
      "Faculty": "VCE AVIATION AEROSPACE ACADEMY",
      "Course Name": "Diploma of Aviation (Aviation Management)",
      "VET Code": "AVI50119",
      "CRICOS Code": "104215"
    },
    {
      "Faculty": "MERIDIAN GRAMMAR SCHOOL OF ENGLISH",
      "Course Name": "English Language Intensive Courses for Overseas Students (ELICOS)",
      "VET Code": "N/A",
      "CRICOS Code": "099847F"
    }
  ],
  "AIBT": [
    {
      "Faculty": "AUSTRALIAN INSTITUTE OF BUSINESS AND TECHNOLOGY",
      "Course Name": "Diploma of Business",
      "VET Code": "BSB50320",
      "CRICOS Code": "089076E"
    }
  ],
  "NPA": [
    {
      "Faculty": "NORTHERN PACIFIC ACADEMY",
      "Course Name": "Certificate IV in Leadership and Management",
      "VET Code": "BSB40520",
      "CRICOS Code": "098765A"
    }
  ]
}`;

  return (
    <ProtectedRoute>
      <div className="flex h-screen overflow-hidden bg-white">
        <Sidebar />
        <main className="flex-1 overflow-auto lg:ml-0 relative bg-white">
          <div className="min-h-full pt-20 lg:pt-0">
            <div className="animate-fade-in-up">
              <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex items-center gap-3 mb-8">
          <FileSpreadsheet className="h-8 w-8 text-green-600" />
          <h1 className="text-3xl font-bold">Google Sheets Import</h1>
        </div>

        {/* Warning Section */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-semibold">Important: Data Import Process</p>
              <p>This tool imports clean course data from Google Sheets to replace corrupted database entries.</p>
              <ul className="text-sm space-y-1 mt-2">
                <li>• Use this to import courses with valid CRICOS codes</li>
                <li>• Optionally clear corrupted data (courses with fake CRICOS codes)</li>
                <li>• After import, use the regular JSON sync for intake dates</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>

        {/* Data Input Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileSpreadsheet className="h-5 w-5" />
              Google Sheets Data
            </CardTitle>
            <CardDescription>
              Paste your Google Sheets data as JSON object. Each sheet (AWT, AIBT, NPA, etc.) should be a key with an array of courses. Each course should have: Faculty, Course Name, VET Code, CRICOS Code
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Sheets Data (JSON Object):</label>
              <textarea
                className="w-full h-64 p-3 border rounded-lg font-mono text-sm bg-gray-50"
                placeholder={sampleData}
                onChange={handleSheetsDataInput}
              />
            </div>

            {Object.keys(sheetsData).length > 0 && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>{Object.keys(sheetsData).length} sheets</strong> loaded with{" "}
                  <strong>
                    {Object.values(sheetsData).reduce((total: number, courses: any) =>
                      total + (Array.isArray(courses) ? courses.length : 0), 0
                    )} total courses
                  </strong>
                </AlertDescription>
              </Alert>
            )}

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="clearExisting"
                checked={clearExisting}
                onChange={(e) => setClearExisting(e.target.checked)}
                className="rounded"
              />
              <label htmlFor="clearExisting" className="text-sm">
                Clear corrupted data before import (recommended)
              </label>
            </div>

            {clearExisting && (
              <div className="bg-red-50 border border-red-200 rounded p-3">
                <div className="flex items-center gap-2 text-red-700">
                  <Trash2 className="h-4 w-4" />
                  <span className="font-medium">Data Clearing Enabled</span>
                </div>
                <p className="text-sm text-red-600 mt-1">
                  This will remove all courses with fake CRICOS codes (starting with &apos;CR&apos;) before importing clean data.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Import Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Import to Database
            </CardTitle>
            <CardDescription>
              Process the Google Sheets data and import to Supabase database
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-semibold text-green-900 mb-2">
                What this import does:
              </h3>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Creates new faculties if they don&apos;t exist</li>
                <li>• Creates new courses with valid CRICOS codes</li>
                <li>• Updates existing courses with clean data</li>
                <li>• Skips courses with invalid CRICOS codes</li>
                <li>• Handles duplicate CRICOS codes gracefully</li>
                <li>• Optionally clears corrupted data first</li>
              </ul>
            </div>

            <Button
              onClick={handleImport}
              disabled={isLoading || Object.keys(sheetsData).length === 0}
              className="w-full"
              size="lg"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <Database className="mr-2 h-4 w-4" />
                  Import to Database
                </>
              )}
            </Button>

            {Object.keys(sheetsData).length === 0 && (
              <p className="text-sm text-gray-500 text-center">
                Please provide Google Sheets data first
              </p>
            )}
          </CardContent>
        </Card>

        {/* Results Section */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {result && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-4">
                <p className="font-semibold">Import completed successfully!</p>

                {/* Summary Stats */}
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="bg-purple-50 p-3 rounded">
                    <p className="font-medium text-purple-900">Sheets</p>
                    <p className="text-purple-700">
                      {result.stats?.sheets?.processed || 0} processed
                    </p>
                    {result.stats?.sheets?.names && result.stats.sheets.names.length > 0 && (
                      <p className="text-xs text-purple-600 mt-1">
                        {result.stats.sheets.names.join(', ')}
                      </p>
                    )}
                  </div>
                  <div className="bg-blue-50 p-3 rounded">
                    <p className="font-medium text-blue-900">Faculties</p>
                    <p className="text-blue-700">
                      {result.stats?.faculties?.created || 0} created,{" "}
                      {result.stats?.faculties?.existing || 0} existing
                    </p>
                  </div>
                  <div className="bg-green-50 p-3 rounded">
                    <p className="font-medium text-green-900">Courses</p>
                    <p className="text-green-700">
                      {result.stats?.courses?.created || 0} created,{" "}
                      {result.stats?.courses?.updated || 0} updated
                    </p>
                  </div>
                </div>

                {/* Skipped Items */}
                {(result.stats?.skipped?.invalidCricos > 0 || result.stats?.skipped?.duplicates > 0) && (
                  <div className="bg-orange-50 p-3 rounded">
                    <p className="font-medium text-orange-900">Skipped Items</p>
                    <p className="text-orange-700 text-sm">
                      {result.stats.skipped.invalidCricos} invalid CRICOS codes,{" "}
                      {result.stats.skipped.duplicates} duplicates
                    </p>
                  </div>
                )}

                {/* Detailed Lists */}
                <div className="space-y-3">
                  {/* Import Errors */}
                  {result.stats?.courses?.errors &&
                    result.stats.courses.errors.length > 0 && (
                      <div>
                        <p className="font-medium text-sm mb-2 text-red-700">
                          Import Issues ({result.stats.courses.errors.length}):
                        </p>
                        <div className="text-xs space-y-1 max-h-40 overflow-y-auto bg-red-50 p-2 rounded border border-red-200">
                          {result.stats.courses.errors.map(
                            (error: string, index: number) => (
                              <div
                                key={index}
                                className="flex items-start gap-2"
                              >
                                <span className="text-red-600 mt-0.5">⚠</span>
                                <span className="text-red-800">{error}</span>
                              </div>
                            )
                          )}
                        </div>
                      </div>
                    )}

                  {/* Created Courses */}
                  {result.stats?.courses?.createdList &&
                    result.stats.courses.createdList.length > 0 && (
                      <div>
                        <p className="font-medium text-sm mb-2">
                          New Courses Created ({result.stats.courses.createdList.length}):
                        </p>
                        <div className="text-xs space-y-1 max-h-40 overflow-y-auto bg-green-50 p-2 rounded">
                          {result.stats.courses.createdList
                            .slice(0, 10)
                            .map((course: string, index: number) => (
                              <div
                                key={index}
                                className="flex items-start gap-2"
                              >
                                <span className="text-green-600 mt-0.5">+</span>
                                <span className="text-green-800">{course}</span>
                              </div>
                            ))}
                          {result.stats.courses.createdList.length > 10 && (
                            <p className="text-green-600 font-medium">
                              ... and {result.stats.courses.createdList.length - 10} more
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                  {/* Updated Courses */}
                  {result.stats?.courses?.existingList &&
                    result.stats.courses.existingList.length > 0 && (
                      <div>
                        <p className="font-medium text-sm mb-2">
                          Courses Updated ({result.stats.courses.existingList.length}):
                        </p>
                        <div className="text-xs space-y-1 max-h-32 overflow-y-auto bg-blue-50 p-2 rounded">
                          {result.stats.courses.existingList
                            .slice(0, 5)
                            .map((course: string, index: number) => (
                              <div
                                key={index}
                                className="flex items-start gap-2"
                              >
                                <span className="text-blue-600 mt-0.5">•</span>
                                <span className="text-blue-800">{course}</span>
                              </div>
                            ))}
                          {result.stats.courses.existingList.length > 5 && (
                            <p className="text-blue-600 font-medium">
                              ... and {result.stats.courses.existingList.length - 5} more
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                </div>

                {/* Next Steps */}
                <div className="bg-blue-50 border border-blue-200 rounded p-3 mt-4">
                  <p className="font-medium text-blue-900 mb-2">Next Steps:</p>
                  <ol className="text-sm text-blue-800 space-y-1">
                    <li>1. Go to the JSON Sync page to sync intake dates</li>
                    <li>2. Upload your intake-dates.json file</li>
                    <li>3. Run the sync to update course intake dates</li>
                  </ol>
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}
      </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
