"use client";

import IntakeDatesViewer from '@/components/IntakeDatesViewer';
import ProtectedRoute from "@/components/ProtectedRoute";
import Sidebar from "@/components/Sidebar";
import { Calendar } from 'lucide-react';

export default function IntakeDatesPage() {
  return (
    <ProtectedRoute>
      <div className="flex h-screen overflow-hidden bg-white">
        <Sidebar />
        <main className="flex-1 overflow-auto lg:ml-0 relative bg-white">
          <div className="min-h-full pt-20 lg:pt-0">
            <div className="animate-fade-in-up">
              <div className="min-h-screen bg-gray-50 p-8">
                <div className="max-w-6xl mx-auto">
                  <div className="text-center mb-8">
                    <div className="flex items-center justify-center gap-3 mb-4">
                      <div className="p-2 bg-blue-600 rounded-lg">
                        <Calendar className="w-8 h-8 text-white" />
                      </div>
                      <h1 className="text-3xl font-bold text-gray-900">Course Intake Dates</h1>
                    </div>
                    <p className="text-gray-600 max-w-2xl mx-auto text-lg">
                      View all available course intake dates across colleges. Use this to identify suitable enrollment opportunities for students.
                    </p>
                  </div>

                  <IntakeDatesViewer />
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
