@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties for Theme System */
:root {
  /* Light Theme Colors */
  --color-primary: 59 130 246;
  --color-primary-foreground: 255 255 255;
  --color-secondary: 99 102 241;
  --color-secondary-foreground: 255 255 255;
  --color-accent: 16 185 129;
  --color-accent-foreground: 255 255 255;
  --color-warning: 245 158 11;
  --color-warning-foreground: 255 255 255;
  --color-error: 239 68 68;
  --color-error-foreground: 255 255 255;

  /* Background Colors */
  --color-background: 255 255 255;
  --color-foreground: 15 23 42;
  --color-card: 255 255 255;
  --color-card-foreground: 15 23 42;
  --color-popover: 255 255 255;
  --color-popover-foreground: 15 23 42;

  /* Border and Input Colors */
  --color-border: 226 232 240;
  --color-input: 226 232 240;
  --color-ring: 59 130 246;

  /* Muted Colors */
  --color-muted: 248 250 252;
  --color-muted-foreground: 100 116 139;

  /* Destructive Colors */
  --color-destructive: 239 68 68;
  --color-destructive-foreground: 255 255 255;

  /* Radius */
  --radius: 0.5rem;
}

[data-theme="dark"] {
  /* Dark Theme Colors */
  --color-primary: 59 130 246;
  --color-primary-foreground: 15 23 42;
  --color-secondary: 71 85 105;
  --color-secondary-foreground: 248 250 252;
  --color-accent: 16 185 129;
  --color-accent-foreground: 15 23 42;
  --color-warning: 245 158 11;
  --color-warning-foreground: 15 23 42;
  --color-error: 239 68 68;
  --color-error-foreground: 248 250 252;

  /* Background Colors */
  --color-background: 2 6 23;
  --color-foreground: 248 250 252;
  --color-card: 2 6 23;
  --color-card-foreground: 248 250 252;
  --color-popover: 2 6 23;
  --color-popover-foreground: 248 250 252;

  /* Border and Input Colors */
  --color-border: 30 41 59;
  --color-input: 30 41 59;
  --color-ring: 59 130 246;

  /* Muted Colors */
  --color-muted: 30 41 59;
  --color-muted-foreground: 148 163 184;

  /* Destructive Colors */
  --color-destructive: 239 68 68;
  --color-destructive-foreground: 248 250 252;
}

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-white text-gray-900 font-sans antialiased;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-neutral-900;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gradient-to-b from-primary-500 to-accent-500 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply from-primary-400 to-accent-400;
  }
}

@layer components {
  /* Modern Form Controls */
  .input-field {
    @apply relative bg-glass-white/5 backdrop-blur-md border border-neutral-700/50
           text-neutral-100 rounded-2xl px-6 py-4 text-base
           focus:ring-2 focus:ring-primary-500/50 focus:border-primary-400/50 focus:outline-none
           transition-all duration-300 ease-out
           placeholder:text-neutral-400
           hover:bg-glass-white/10 hover:border-neutral-600/50
           disabled:opacity-50 disabled:cursor-not-allowed;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .input-field:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .input-field.error {
    @apply border-error-500/70 focus:ring-error-500/50 focus:border-error-400/50;
    box-shadow: 0 4px 20px rgba(239, 68, 68, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .input-field.success {
    @apply border-success-500/70 focus:ring-success-500/50 focus:border-success-400/50;
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  /* Floating Label Effect */
  .floating-label {
    @apply absolute left-6 top-4 text-neutral-400 text-base pointer-events-none
           transition-all duration-300 ease-out;
  }

  .input-field:focus + .floating-label,
  .input-field:not(:placeholder-shown) + .floating-label {
    @apply text-xs text-primary-400 -translate-y-8 translate-x-0 bg-neutral-900 px-2 rounded;
  }

  /* Modern Button Styles */
  .btn-primary {
    @apply relative bg-gradient-to-r from-primary-600 to-primary-500 hover:from-primary-500 hover:to-primary-400
           text-white px-8 py-4 rounded-2xl font-semibold text-base
           transition-all duration-300 ease-out transform hover:scale-105 hover:-translate-y-1
           focus:ring-4 focus:ring-primary-500/50 focus:outline-none
           disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:translate-y-0
           shadow-lg hover:shadow-xl;
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
  }

  .btn-primary:hover {
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4);
  }

  .btn-primary:active {
    transform: scale(0.98) translateY(0);
  }

  .btn-secondary {
    @apply relative bg-glass-white/10 backdrop-blur-md hover:bg-glass-white/20
           text-neutral-100 px-8 py-4 rounded-2xl font-semibold text-base
           border border-neutral-600/50 hover:border-neutral-500/50
           transition-all duration-300 ease-out transform hover:scale-105 hover:-translate-y-1
           focus:ring-4 focus:ring-neutral-500/50 focus:outline-none
           disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:translate-y-0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .btn-secondary:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .btn-accent {
    @apply relative bg-gradient-to-r from-accent-600 to-accent-500 hover:from-accent-500 hover:to-accent-400
           text-white px-8 py-4 rounded-2xl font-semibold text-base
           transition-all duration-300 ease-out transform hover:scale-105 hover:-translate-y-1
           focus:ring-4 focus:ring-accent-500/50 focus:outline-none
           disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:translate-y-0
           shadow-lg hover:shadow-xl;
    box-shadow: 0 10px 25px rgba(217, 70, 239, 0.3);
  }

  .btn-accent:hover {
    box-shadow: 0 15px 35px rgba(217, 70, 239, 0.4);
  }

  /* Modern Card Styles */
  .card {
    @apply relative bg-glass-white/5 backdrop-blur-xl rounded-3xl p-8
           border border-neutral-700/50
           shadow-glass hover:shadow-xl
           transition-all duration-500 ease-out hover:-translate-y-2;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  }

  .card:hover {
    @apply border-neutral-600/50;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .card-gradient {
    @apply card relative overflow-hidden;
  }

  .card-gradient::before {
    content: '';
    @apply absolute inset-0 opacity-0 transition-opacity duration-500;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(217, 70, 239, 0.1) 100%);
    border-radius: inherit;
  }

  .card-gradient:hover::before {
    @apply opacity-100;
  }

  /* Glass Morphism Navigation */
  .nav-link {
    @apply relative block px-6 py-4 text-neutral-300 hover:text-white
           rounded-2xl font-medium text-base
           transition-all duration-300 ease-out
           hover:bg-glass-white/10 hover:backdrop-blur-md;
  }

  .nav-link:hover {
    transform: translateX(8px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .nav-link.active {
    @apply bg-gradient-to-r from-primary-600/80 to-primary-500/80 text-white backdrop-blur-md;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .nav-link.active::before {
    content: '';
    @apply absolute left-0 top-1/2 w-1 h-8 bg-gradient-to-b from-primary-400 to-accent-400 rounded-r-full;
    transform: translateY(-50%);
  }

  /* Status Messages */
  .error-message {
    @apply flex items-center gap-2 text-error-400 text-sm mt-2 p-3
           bg-error-500/10 border border-error-500/20 rounded-xl
           animate-fade-in-up;
  }

  .success-message {
    @apply flex items-center gap-2 text-success-400 text-sm mt-2 p-3
           bg-success-500/10 border border-success-500/20 rounded-xl
           animate-fade-in-up;
  }

  .info-message {
    @apply flex items-center gap-2 text-primary-400 text-sm mt-2 p-3
           bg-primary-500/10 border border-primary-500/20 rounded-xl
           animate-fade-in-up;
  }

  .warning-message {
    @apply flex items-center gap-2 text-warning-400 text-sm mt-2 p-3
           bg-warning-500/10 border border-warning-500/20 rounded-xl
           animate-fade-in-up;
  }

  /* Loading Animations */
  .skeleton {
    @apply bg-gradient-to-r from-neutral-800 to-neutral-700 rounded-2xl animate-pulse;
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  /* Progress Indicators */
  .progress-bar {
    @apply relative h-2 bg-neutral-800 rounded-full overflow-hidden;
  }

  .progress-fill {
    @apply h-full bg-gradient-to-r from-primary-500 to-accent-500 rounded-full
           transition-all duration-700 ease-out;
  }

  /* Dropdown Styles */
  .dropdown {
    @apply relative bg-glass-white/5 backdrop-blur-md border border-neutral-700/50
           text-neutral-100 rounded-2xl px-6 py-4 text-base
           focus:ring-2 focus:ring-primary-500/50 focus:border-primary-400/50 focus:outline-none
           transition-all duration-300 ease-out
           hover:bg-glass-white/10 hover:border-neutral-600/50
           disabled:opacity-50 disabled:cursor-not-allowed appearance-none cursor-pointer;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 1.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 3.5rem;
  }

  /* Modal and Overlay Styles */
  .modal-overlay {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-50
           flex items-center justify-center p-4
           animate-fade-in;
  }

  .modal {
    @apply bg-glass-white/10 backdrop-blur-xl border border-neutral-700/50
           rounded-3xl p-8 max-w-lg w-full
           shadow-glass animate-scale-in;
  }

  /* Tooltip Styles */
  .tooltip {
    @apply absolute z-50 px-3 py-2 text-sm text-white
           bg-neutral-900/90 backdrop-blur-md rounded-xl
           border border-neutral-700/50
           shadow-lg pointer-events-none
           animate-fade-in-up;
  }

  /* Badge Styles */
  .badge {
    @apply inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-primary-500/20 text-primary-300 border border-primary-500/30;
  }

  .badge-success {
    @apply badge bg-success-500/20 text-success-300 border border-success-500/30;
  }

  .badge-warning {
    @apply badge bg-warning-500/20 text-warning-300 border border-warning-500/30;
  }

  .badge-error {
    @apply badge bg-error-500/20 text-error-300 border border-error-500/30;
  }

  /* Icon Animations */
  .icon-spin {
    animation: spin 1s linear infinite;
  }

  .icon-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .icon-bounce {
    animation: bounce 1s infinite;
  }

  /* Glass Container for Sidebar */
  .glass-container {
    @apply bg-glass-white/5 backdrop-blur-xl border-r border-neutral-700/50;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.02) 100%);
  }
}
